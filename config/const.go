package config

import (
	"fmt"
	"time"
)

const (
	RedisKeyLockActivity         = "activity:lock:%d"
	RedisKeyLockActivityProgress = "activity:progress:lock:%d"
)

const (
	// activity:cache:$playerId:$activityType
	RedisKeyCacheActivity = "activity:cache:%d:%d"

	// activity:progress:$playerId:$activityType
	RedisKeyCacheActivityProgress = "activity:progress:%d:%d"
)

// 爆护之路活动相关Redis Key (按照需求文档设计)
const (
	// 活动周期管理Key: activity:{activity_id}
	RedisKeyActivityCycle = "activity:%d"

	// 玩家活动数据Key: act:{activity_id}:{player_id}:{cycle_id}
	RedisKeyPlayerActivityData = "act:%d:%d:%d"

	// 分布式锁Key: activity:explosive_protection:lock:{activity_id}:cycle_create
	RedisKeyExplosiveProtectionCycleLock = "activity:explosive_protection:lock:%d:cycle_create"
)

const (
	ActivityCacheExpire = 3 * 86400 * time.Second

	// 爆护之路相关过期时间 (按照需求文档设计)
	ActivityCycleBaseTTL               = 7 * 24 * time.Hour // 基础TTL: 7天
	PlayerActivityDataBaseTTL          = 7 * 24 * time.Hour // 基础TTL: 7天
	ExplosiveProtectionCycleLockExpire = 30 * time.Second   // 周期创建锁30秒
)

// 活动修改分布式锁
func DLMActivityLockKey(playerId uint64) string {
	return fmt.Sprintf(RedisKeyLockActivity, playerId)
}

// 活动进度分布式锁
func DLMActivityProgressLockKey(playerId uint64) string {
	return fmt.Sprintf(RedisKeyLockActivityProgress, playerId)
}

// 活动周期管理Key
func ActivityCycleKey(activityId int64) string {
	return fmt.Sprintf(RedisKeyActivityCycle, activityId)
}

// 玩家活动数据Key
func PlayerActivityDataKey(activityId int64, playerId uint64, cycleId int32) string {
	return fmt.Sprintf(RedisKeyPlayerActivityData, activityId, playerId, cycleId)
}

// 爆护之路周期创建锁Key
func ExplosiveProtectionCycleLockKey(activityId int64) string {
	return fmt.Sprintf(RedisKeyExplosiveProtectionCycleLock, activityId)
}

// 计算TTL时间 (当前周期剩余天数 + 周期天数 + 1 + 随机1天)
func CalculateActivityTTL(cycleDays int32, remainingDays int32) time.Duration {
	baseDays := remainingDays + cycleDays + 1
	randomHours := time.Duration(1+(time.Now().UnixNano()%24)) * time.Hour // 随机1-24小时
	return time.Duration(baseDays)*24*time.Hour + randomHours
}
