package dao_activity

import (
	"activitysrv/config"
	"activitysrv/internal/model"
	"context"
	"errors"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/frameworks/lib/reflectext"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"strconv"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
)

// GetCurrentCycle 获取当前活动周期
func GetCurrentCycle(ctx context.Context, activityId int64) (*model.ActivityCycle, error) {
	key := config.ActivityCycleKey(activityId)
	redisCli := redisx.GetRedisCli("activity", 0)

	// 使用 HGETALL 获取所有字段
	hashMap, err := redisCli.HGetAll(ctx, key).Result()
	if err != nil {
		return nil, fmt.Errorf("获取当前周期失败: %w", err)
	}

	// 如果没有数据，返回nil表示周期不存在
	if len(hashMap) == 0 {
		return nil, nil
	}

	// 解析周期ID
	cycleIdStr, exists := hashMap["cycle"]
	if !exists {
		return nil, fmt.Errorf("周期数据缺少cycle字段")
	}
	cycleId, err := strconv.ParseInt(cycleIdStr, 10, 32)
	if err != nil {
		return nil, fmt.Errorf("解析周期ID失败: %w", err)
	}

	// 解析结束时间
	endTimeStr, exists := hashMap["end_time"]
	if !exists {
		return nil, fmt.Errorf("周期数据缺少end_time字段")
	}
	endTime, err := strconv.ParseInt(endTimeStr, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("解析结束时间失败: %w", err)
	}

	// 解析开始时间（可选字段，如果不存在则根据结束时间推算）
	var startTime int64
	if startTimeStr, exists := hashMap["start_time"]; exists {
		startTime, err = strconv.ParseInt(startTimeStr, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("解析开始时间失败: %w", err)
		}
	} else {
		// 如果没有开始时间，根据结束时间和默认周期长度推算
		// 这里假设默认周期为7天，实际应该从配置中获取
		startTime = endTime - 7*24*3600
	}

	// 解析状态（可选字段，默认为活跃状态）
	status := int32(1) // 默认活跃状态
	if statusStr, exists := hashMap["status"]; exists {
		statusInt, err := strconv.ParseInt(statusStr, 10, 32)
		if err != nil {
			return nil, fmt.Errorf("解析状态失败: %w", err)
		}
		status = int32(statusInt)
	}

	// 解析创建时间（可选字段，默认为当前时间）
	createdAt := time.Now().Unix()
	if createdAtStr, exists := hashMap["created_at"]; exists {
		createdAt, err = strconv.ParseInt(createdAtStr, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("解析创建时间失败: %w", err)
		}
	}

	// 构建周期对象
	cycle := &model.ActivityCycle{
		CycleId:   int32(cycleId),
		StartTime: startTime,
		EndTime:   endTime,
		Status:    status,
		CreatedAt: createdAt,
	}

	// 数据验证
	if cycle.CycleId <= 0 {
		return nil, fmt.Errorf("无效的周期ID: %d", cycle.CycleId)
	}
	if cycle.EndTime <= cycle.StartTime {
		return nil, fmt.Errorf("无效的时间范围: startTime=%d, endTime=%d", cycle.StartTime, cycle.EndTime)
	}

	logrus.Debugf("成功获取活动周期: activityId=%d, cycleId=%d, startTime=%d, endTime=%d, status=%d",
		activityId, cycle.CycleId, cycle.StartTime, cycle.EndTime, cycle.Status)

	return cycle, nil
}

// CreateNewCycle 创建新的活动周期
func CreateNewCycle(ctx context.Context, activityCfg *cmodel.Activity) (*model.ActivityCycle, error) {
	// 使用分布式锁防止并发创建
	lockKey := config.ExplosiveProtectionCycleLockKey(activityCfg.Id)
	redisCli := redisx.GetRedisCli("activity", 0)

	// 尝试获取锁
	lockValue := fmt.Sprintf("%d_%d", time.Now().UnixNano(), activityCfg.Id)
	acquired, err := redisCli.SetNX(ctx, lockKey, lockValue, config.ExplosiveProtectionCycleLockExpire).Result()
	if err != nil {
		return nil, fmt.Errorf("获取分布式锁失败: %w", err)
	}
	if !acquired {
		return nil, fmt.Errorf("其他进程正在创建周期，请稍后重试")
	}

	// 确保释放锁
	defer func() {
		redisCli.Del(ctx, lockKey)
	}()

	// 再次检查是否已经有当前周期（双重检查）
	existingCycle, err := GetCurrentCycle(ctx, activityCfg.Id)
	if err != nil {
		return nil, err
	}
	if existingCycle != nil {
		return existingCycle, nil
	}

	// 获取下一个周期ID
	nextCycleId, err := getNextCycleId(ctx, activityCfg)
	if err != nil {
		return nil, fmt.Errorf("获取下一个周期ID失败: %w", err)
	}

	// 计算周期时间
	startTime, endTime, err := calculateCycleTime(activityCfg, nextCycleId)
	if err != nil {
		return nil, fmt.Errorf("计算周期时间失败: %w", err)
	}

	// 创建新周期
	newCycle := model.NewActivityCycle(nextCycleId, startTime, endTime)

	// 保存到Redis
	if err := saveCurrentCycle(ctx, activityCfg.Id, newCycle); err != nil {
		return nil, fmt.Errorf("保存当前周期失败: %w", err)
	}

	logrus.Infof("成功创建新的活动周期: activityId=%d, cycleId=%d, startTime=%d, endTime=%d",
		activityCfg.Id, newCycle.CycleId, newCycle.StartTime, newCycle.EndTime)

	return newCycle, nil
}

// CheckAndCreateCycleIfNeeded 检查并在需要时创建新周期
func CheckAndCreateCycleIfNeeded(ctx context.Context, activityCfg *cmodel.Activity) (*model.ActivityCycle, error) {
	currentCycle, err := GetCurrentCycle(ctx, activityCfg.Id)

	if err != nil {
		return nil, err
	}

	// 如果没有当前周期或当前周期已过期，创建新周期
	if currentCycle == nil || currentCycle.IsExpired() {
		return CreateNewCycle(ctx, activityCfg)
	}

	return currentCycle, nil
}

// saveCurrentCycle 保存当前周期到Redis (按照需求文档格式)
func saveCurrentCycle(ctx context.Context, activityId int64, cycle *model.ActivityCycle) error {
	key := config.ActivityCycleKey(activityId)
	redisCli := redisx.GetRedisCli("activity", 0)

	// 数据验证
	if cycle == nil {
		return fmt.Errorf("周期数据不能为空")
	}
	if cycle.CycleId <= 0 {
		return fmt.Errorf("无效的周期ID: %d", cycle.CycleId)
	}
	if cycle.EndTime <= cycle.StartTime {
		return fmt.Errorf("无效的时间范围: startTime=%d, endTime=%d", cycle.StartTime, cycle.EndTime)
	}

	// 计算剩余天数
	now := time.Now().Unix()
	remainingDays := int32((cycle.EndTime - now) / (24 * 3600))
	if remainingDays < 0 {
		remainingDays = 0
	}

	// 计算周期天数
	cycleDays := int32((cycle.EndTime - cycle.StartTime) / (24 * 3600))

	// 计算TTL (当前周期剩余天数 + 周期天数 + 1 + 随机1天)
	ttl := config.CalculateActivityTTL(cycleDays, remainingDays)

	// 使用 HSET 保存所有字段
	pipe := redisCli.Pipeline()
	pipe.HSet(ctx, key, "cycle", cycle.CycleId)
	pipe.HSet(ctx, key, "start_time", cycle.StartTime)
	pipe.HSet(ctx, key, "end_time", cycle.EndTime)
	pipe.HSet(ctx, key, "status", cycle.Status)
	pipe.HSet(ctx, key, "created_at", cycle.CreatedAt)
	pipe.Expire(ctx, key, ttl)

	_, err := pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("保存周期数据到Redis失败: %w", err)
	}

	logrus.Infof("成功保存活动周期到Redis: activityId=%d, cycleId=%d, key=%s, ttl=%v",
		activityId, cycle.CycleId, key, ttl)

	return nil
}

// GetPreviousCycle 获取上一个周期数据 (用于奖励领取)
func GetPreviousCycle(ctx context.Context, activityId int64) (*model.ActivityCycle, error) {
	// 获取当前周期
	currentCycle, err := GetCurrentCycle(ctx, activityId)
	if err != nil {
		return nil, err
	}

	if currentCycle == nil || currentCycle.CycleId <= 1 {
		// 没有当前周期或当前是第一个周期，没有上一个周期
		return nil, nil
	}

	// 构建上一个周期数据 (简化实现，实际可能需要更复杂的逻辑)
	previousCycle := &model.ActivityCycle{
		CycleId:   currentCycle.CycleId - 1,
		StartTime: currentCycle.StartTime - (currentCycle.EndTime - currentCycle.StartTime),
		EndTime:   currentCycle.StartTime,
		Status:    2, // 已结束状态
		CreatedAt: currentCycle.CreatedAt,
	}

	return previousCycle, nil
}

// getNextCycleId 获取下一个周期ID，根据活动配置智能计算
func getNextCycleId(ctx context.Context, activityCfg *cmodel.Activity) (int32, error) {
	// 1. 验证活动时间配置
	now := time.Now().Unix()
	if now < activityCfg.OpenAt {
		return 0, fmt.Errorf("活动尚未开始: activityId=%d, openAt=%d, now=%d",
			activityCfg.Id, activityCfg.OpenAt, now)
	}

	if activityCfg.CloseAt > 0 && now > activityCfg.CloseAt {
		return 0, fmt.Errorf("活动已结束: activityId=%d, closeAt=%d, now=%d",
			activityCfg.Id, activityCfg.CloseAt, now)
	}

	// 2. 根据是否循环来计算周期ID
	if !activityCfg.IsLoop {
		// 非循环活动，周期固定为1
		logrus.Debugf("非循环活动，周期ID固定为1: activityId=%d", activityCfg.Id)
		return 1, nil
	}

	// 3. 循环活动：根据活动开始时间和周期天数计算当前周期
	if activityCfg.CycleDays <= 0 {
		return 0, fmt.Errorf("循环活动的周期天数配置无效: activityId=%d, cycleDays=%d",
			activityCfg.Id, activityCfg.CycleDays)
	}

	// 计算从活动开始到现在经过了多少秒
	elapsedSeconds := now - activityCfg.OpenAt
	if elapsedSeconds < 0 {
		// 理论上不应该发生，因为前面已经检查过了
		return 1, nil
	}

	// 计算每个周期的秒数
	cycleSeconds := int64(activityCfg.CycleDays) * 24 * 3600

	// 计算当前应该是第几个周期（从1开始）
	currentCycleId := int32(elapsedSeconds/cycleSeconds) + 1

	// 4. 验证计算结果的合理性
	if currentCycleId <= 0 {
		logrus.Warnf("计算出的周期ID异常，重置为1: activityId=%d, calculated=%d",
			activityCfg.Id, currentCycleId)
		return 1, nil
	}

	// 5. 检查是否超出活动结束时间
	if activityCfg.CloseAt > 0 {
		// 计算当前周期的结束时间
		currentCycleStartTime := activityCfg.OpenAt + int64(currentCycleId-1)*cycleSeconds
		currentCycleEndTime := currentCycleStartTime + cycleSeconds

		if currentCycleEndTime > activityCfg.CloseAt {
			// 当前周期会超出活动结束时间，需要调整
			maxCycleId := int32((activityCfg.CloseAt-activityCfg.OpenAt)/cycleSeconds) + 1
			if currentCycleId > maxCycleId {
				logrus.Warnf("当前周期超出活动结束时间，调整为最大周期: activityId=%d, calculated=%d, max=%d",
					activityCfg.Id, currentCycleId, maxCycleId)
				currentCycleId = maxCycleId
			}
		}
	}

	logrus.Infof("计算循环活动周期ID: activityId=%d, openAt=%d, now=%d, cycleDays=%d, cycleId=%d",
		activityCfg.Id, activityCfg.OpenAt, now, activityCfg.CycleDays, currentCycleId)

	return currentCycleId, nil
}

// calculateCycleTime 计算周期的开始和结束时间
func calculateCycleTime(activityCfg *cmodel.Activity, cycleId int32) (startTime, endTime int64, err error) {
	if !activityCfg.IsLoop {
		// 非循环活动：周期时间就是活动时间
		startTime = activityCfg.OpenAt
		if activityCfg.CloseAt > 0 {
			endTime = activityCfg.CloseAt
		} else {
			// 如果没有结束时间，使用默认的周期长度
			cycleDays := activityCfg.CycleDays
			if cycleDays <= 0 {
				cycleDays = 7 // 默认7天
			}
			endTime = startTime + int64(cycleDays)*24*3600
		}

		logrus.Debugf("非循环活动周期时间: activityId=%d, cycleId=%d, startTime=%d, endTime=%d",
			activityCfg.Id, cycleId, startTime, endTime)
		return startTime, endTime, nil
	}

	// 循环活动：根据周期ID计算具体的周期时间
	if activityCfg.CycleDays <= 0 {
		return 0, 0, fmt.Errorf("循环活动的周期天数配置无效: activityId=%d, cycleDays=%d",
			activityCfg.Id, activityCfg.CycleDays)
	}

	// 计算每个周期的秒数
	cycleSeconds := int64(activityCfg.CycleDays) * 24 * 3600

	// 计算当前周期的开始时间
	startTime = activityCfg.OpenAt + int64(cycleId-1)*cycleSeconds
	endTime = startTime + cycleSeconds

	// 确保不超出活动结束时间
	if activityCfg.CloseAt > 0 && endTime > activityCfg.CloseAt {
		endTime = activityCfg.CloseAt
		logrus.Warnf("周期结束时间超出活动结束时间，已调整: activityId=%d, cycleId=%d, original=%d, adjusted=%d",
			activityCfg.Id, cycleId, startTime+cycleSeconds, endTime)
	}

	// 验证时间的合理性
	if startTime >= endTime {
		return 0, 0, fmt.Errorf("计算出的周期时间无效: activityId=%d, cycleId=%d, startTime=%d, endTime=%d",
			activityCfg.Id, cycleId, startTime, endTime)
	}

	logrus.Debugf("循环活动周期时间: activityId=%d, cycleId=%d, startTime=%d, endTime=%d, cycleDays=%d",
		activityCfg.Id, cycleId, startTime, endTime, activityCfg.CycleDays)

	return startTime, endTime, nil
}
